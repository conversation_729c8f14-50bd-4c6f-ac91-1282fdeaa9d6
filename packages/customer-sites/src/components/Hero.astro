---
import PropertySearchTabs from "../components/PropertySearchTabs.jsx";

// Get website info and theme from Astro.locals (set by middleware)
// Add fallback values in case the properties don't exist in Astro.locals
const defaultWebsiteTheme = {
  primary_color: "#3B82F6",
  secondary_color: "#10B981",
  font_family: "Inter",
};

// Get values from Astro.locals
const { websiteInfo, websiteTheme } = Astro.locals || {};

// Use fallbacks if values are not available
const theme = websiteTheme || defaultWebsiteTheme;

// Calculate a darker version of the primary color for headings
import Color from "colorjs.io";

// Create a darker version of primary_color for headings
let headingColor = "#6b0f2b"; // Default fallback color
let lighterHeadingColor = "#6b0f2b"; // Default fallback color
if (theme?.primary_color) {
  try {
    // Create a Color object from the primary color
    const primaryColor = new Color(theme.primary_color);

    // Mix with black to darken it (30% black, 70% primary color)
    const darkColor = Color.mix(primaryColor, "black", 0.3, { space: "lch" });

    // Convert to sRGB and get the hex string
    headingColor = darkColor.to("srgb").toString({ format: "hex" });
    lighterHeadingColor = Color.mix(primaryColor, "white", 0.9, {
      space: "lch",
    })
      .to("srgb")
      .toString({ format: "hex" });
  } catch (error) {
    console.error("Error calculating heading color:", error);
    // Keep the default fallback color if there's an error
  }
}

// No need to fetch website info here as it's already available in Astro.locals

// Get the hero image URL if available from the website object
const heroImageUrl = websiteInfo?.website?.hero_image_url;

// Defining the hero image source
// Use the website's hero image if available, otherwise use a default image
const baseImageUrl =
  heroImageUrl ||
  "https://images.unsplash.com/photo-1512699355324-f07e3106dae5";

// If it's our own image, we don't need to use wsrv.nl optimization
const heroImage = {
  src: heroImageUrl
    ? baseImageUrl
    : `https://wsrv.nl/?url=${baseImageUrl}&w=1920&output=webp&q=85`,
  width: 1920,
  height: 1080,
};

// Log the hero image URL for debugging
console.log("Hero component: heroImageUrl:", heroImageUrl);
console.log("Hero component: final image src:", heroImage.src);

// Extract website address for the search component
const websiteAddress = websiteInfo?.website?.address || "";
---

<!-- Full-width wrapper -->
<div
  class="relative w-full flex h-[60vh] mb-24 lg:mb-16 bg-cover bg-center items-center justify-center"
  style={`background-image: url('${heroImage.src}');`}
>
  <div
    class="bg-gradient-to-t from-transparent to-black/80 w-full h-full absolute"
  >
  </div>
  <div class="w-full flex items-center -mt-16">
    <!-- 40rem is 50% of the 80rem container max-width -->
    <div class="lg:p-12 w-full flex flex-col items-center justify-center">
      <h1
        class="!text-white/90 text-xl md:text-2xl lg:text-3xl xl:text-4xl text-center line-clamp-2 font-bold mb-1 heading-color text-shadow-[0_0_4px_rgba(0,0,0,0.1),0_0_16px_rgba(0,0,0,0.1),0_0_64px_rgba(0,0,0,0.1)] p-12 z-10"
      >
        {websiteInfo?.title}
      </h1>
      <!-- <p
          class="text-md md:text-lg line-clamp-4 text-white/90 md:text-gray-600 mb-12"
        >
          {websiteInfo?.description}
        </p> -->

      <!-- Property Search Tabs Component -->
      <PropertySearchTabs
        client:load
        primaryColor={theme.primary_color}
        headingColor={headingColor}
        teamId={websiteInfo?.teamId}
        websiteAddress={websiteAddress}
      />
    </div>
  </div>
  <!-- Swiper Container -->
  <div
    class="w-full absolute z-40 -bottom-24 lg:-bottom-20 h-40 overflow-hidden"
  >
    <!-- Container with same max-width and padding as FeaturedProperties for alignment -->
    <div class="max-w-7xl mx-auto px-4 h-full">
      <div class="swiper hero-cards-swiper h-full py-4">
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <div
              class="hover:scale-105 transition-transform duration-300 cursor-pointer bg-white rounded-xl p-6 h-32 border border-gray-200 shadow-lg hover:shadow-xl"
            >
              <div class="w-full h-full z-10 flex items-center justify-center">
                <span class="text-gray-600 font-medium">Card Content 1</span>
              </div>
            </div>
          </div>
          <div class="swiper-slide">
            <div
              class="hover:scale-105 transition-transform duration-300 cursor-pointer bg-white rounded-xl p-6 h-32 border border-gray-200 shadow-lg hover:shadow-xl"
            >
              <div class="w-full h-full z-10 flex items-center justify-center">
                <span class="text-gray-600 font-medium">Card Content 2</span>
              </div>
            </div>
          </div>
          <div class="swiper-slide">
            <div
              class="hover:scale-105 transition-transform duration-300 cursor-pointer bg-white rounded-xl p-6 h-32 border border-gray-200 shadow-lg hover:shadow-xl"
            >
              <div class="w-full h-full z-10 flex items-center justify-center">
                <span class="text-gray-600 font-medium">Card Content 3</span>
              </div>
            </div>
          </div>
          <div class="swiper-slide">
            <div
              class="hover:scale-105 transition-transform duration-300 cursor-pointer bg-white rounded-xl p-6 h-32 border border-gray-200 shadow-lg hover:shadow-xl"
            >
              <div class="w-full h-full z-10 flex items-center justify-center">
                <span class="text-gray-600 font-medium">Card Content 4</span>
              </div>
            </div>
          </div>
          <div class="swiper-slide">
            <div
              class="hover:scale-105 transition-transform duration-300 cursor-pointer bg-white rounded-xl p-6 h-32 border border-gray-200 shadow-lg hover:shadow-xl"
            >
              <div class="w-full h-full z-10 flex items-center justify-center">
                <span class="text-gray-600 font-medium">Card Content 5</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation buttons -->
        <div
          class="swiper-button-next !text-white !bg-black/30 !rounded-full !w-12 !h-12 !mt-0 !top-1/2 !-translate-y-1/2 !right-4 hover:!bg-black/50 !transition-colors !duration-200"
        >
        </div>
        <div
          class="swiper-button-prev !text-white !bg-black/30 !rounded-full !w-12 !h-12 !mt-0 !top-1/2 !-translate-y-1/2 !left-4 hover:!bg-black/50 !transition-colors !duration-200"
        >
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Swiper CSS -->
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"
/>

<!-- Swiper JS -->
<script>
  import { Swiper } from "swiper/bundle";

  // Initialize Swiper when the DOM is loaded
  document.addEventListener("DOMContentLoaded", function () {
    new Swiper(".hero-cards-swiper", {
      // Mobile: Each card takes 80% of screen width, slide exactly one card at a time
      // Desktop: Show multiple cards with proper spacing
      slidesPerView: "auto",
      spaceBetween: 16,
      centeredSlides: false,
      loop: false,
      slidesPerGroup: 1, // Always move one slide at a time

      // Prevent sliding past the last card
      resistance: true,
      resistanceRatio: 0, // No resistance - hard stop at edges
      allowTouchMove: true,
      watchSlidesProgress: true,
      preventInteractionOnTransition: true,

      // Navigation buttons
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },

      // Responsive breakpoints
      breakpoints: {
        // Mobile: 80% width cards, slide exactly one card
        320: {
          slidesPerView: 1,
          spaceBetween: 16,
          slidesPerGroup: 1,
        },
        // Small tablets
        640: {
          slidesPerView: 2,
          spaceBetween: 20,
          slidesPerGroup: 1,
        },
        // Medium tablets
        768: {
          slidesPerView: 2.5,
          spaceBetween: 24,
          slidesPerGroup: 1,
        },
        // Desktop
        1024: {
          slidesPerView: 3.5,
          spaceBetween: 24,
          slidesPerGroup: 1,
        },
        // Large desktop
        1280: {
          slidesPerView: 4,
          spaceBetween: 24,
          slidesPerGroup: 1,
        },
        // Extra large displays
        1536: {
          slidesPerView: 5,
          spaceBetween: 24,
          slidesPerGroup: 1,
        },
      },

      // Event handlers
      on: {
        init: function () {
          // Swiper initialized
        },
        slideChange: function () {
          // Slide changed
        },
      },
    });
  });
</script>

<style define:vars={{ headingColor, lighterHeadingColor }}>
  .heading-color {
    color: var(--headingColor);
    border-color: var(--headingColor);
    @media screen and (max-width: 768px) {
      color: var(--lighterHeadingColor);
      border-color: var(--lighterHeadingColor);
    }
  }

  /* Swiper custom styles */
  .hero-cards-swiper {
    overflow: visible !important;
    padding: 16px 0 !important;
  }

  .hero-cards-swiper .swiper-wrapper {
    overflow: visible !important;
  }

  .hero-cards-swiper .swiper-slide {
    overflow: visible !important;
    /* Mobile: 80% width with proper spacing */
    width: 80vw !important;
    min-width: 280px;
  }

  /* Desktop breakpoints for slide widths */
  @media (min-width: 640px) {
    .hero-cards-swiper .swiper-slide {
      width: calc(50% - 10px) !important;
      min-width: 300px;
    }
  }

  @media (min-width: 768px) {
    .hero-cards-swiper .swiper-slide {
      width: calc(40% - 12px) !important;
      min-width: 320px;
    }
  }

  @media (min-width: 1024px) {
    .hero-cards-swiper .swiper-slide {
      width: calc(28.57% - 18px) !important;
      min-width: 280px;
    }
  }

  @media (min-width: 1280px) {
    .hero-cards-swiper .swiper-slide {
      width: calc(25% - 18px) !important;
      min-width: 280px;
    }
  }

  @media (min-width: 1536px) {
    .hero-cards-swiper .swiper-slide {
      width: calc(20% - 19.2px) !important;
      min-width: 260px;
    }
  }

  /* Navigation button improvements */
  .hero-cards-swiper .swiper-button-next,
  .hero-cards-swiper .swiper-button-prev {
    transition: all 0.2s ease-in-out;
  }

  .hero-cards-swiper .swiper-button-next:hover,
  .hero-cards-swiper .swiper-button-prev:hover {
    transform: translateY(-50%) scale(1.1);
  }

  /* Hide navigation buttons on mobile for cleaner look */
  @media (max-width: 767px) {
    .hero-cards-swiper .swiper-button-next,
    .hero-cards-swiper .swiper-button-prev {
      display: none;
    }
  }

  .hero-cards-swiper .swiper-button-next,
  .hero-cards-swiper .swiper-button-prev {
    color: white !important;
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    margin-top: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
  }

  .hero-cards-swiper .swiper-button-next:after,
  .hero-cards-swiper .swiper-button-prev:after {
    font-size: 16px !important;
    font-weight: bold !important;
  }

  .hero-cards-swiper .swiper-button-next {
    right: 8px !important;
  }

  .hero-cards-swiper .swiper-button-prev {
    left: 8px !important;
  }

  .hero-cards-swiper .swiper-button-next:hover,
  .hero-cards-swiper .swiper-button-prev:hover {
    background: rgba(0, 0, 0, 0.4) !important;
  }
</style>
